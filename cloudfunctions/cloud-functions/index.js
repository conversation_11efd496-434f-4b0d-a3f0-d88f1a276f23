const cloud = require("wx-server-sdk");
const { getHolidayData, getWeekendData } = require('./api/holiday');
const { getAnnouncementList } = require('./api/announcements');
const { getUserInfo, updateUserInfo, getVipRecords, getVipRecordsStats, getUserRedemptionCodes, useRedemptionCode } = require('./api/user');
const { checkIn, getCheckInStatus, getCheckInCalendar, getCheckInHistory } = require('./api/check-in');
const { getPointsBalance, getPointsRecords, getPointsStats } = require('./api/points');
const {
  getStoreItems, purchaseItem, getMyRedemptionCodes, redeemCode,
  initializeStoreItems
} = require('./api/store');
const { getCloudDataInfo, downloadUserData, uploadUserData, getHistoryDataList, getHistoryData, getCloudDataStats, clearCloudData } = require('./api/user-data');
const { processInvitation, getInvitationStats } = require('./api/invitation');
const { startFishingStatus, endFishingStatus, getCurrentFishingCount, cleanupExpiredFishingStatus, getUserFishingStatus } = require('./api/fishing-status');
const { submitFeedback, getUserFeedbacks, deleteFeedback, getFeedbackStats } = require('./api/feedback');
const { getFriendApps, createFriendApp, updateFriendApp, deleteFriendApp, toggleFriendAppVisibility, getFriendAppStats } = require('./api/friend-apps');
const { getAllConfigs } = require('./api/config');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
});

// 获取openid
const getOpenId = async () => {
  // 获取基础信息
  const wxContext = cloud.getWXContext();
  return {
    openid: wxContext.OPENID,
    appid: wxContext.APPID,
    unionid: wxContext.UNIONID,
  };
};

// 获取小程序二维码
const getMiniProgramCode = async () => {
  // 获取小程序二维码的buffer
  const resp = await cloud.openapi.wxacode.get({
    path: "pages/index/index",
  });
  const { buffer } = resp;
  // 将图片上传云存储空间
  const upload = await cloud.uploadFile({
    cloudPath: "code.png",
    fileContent: buffer,
  });
  return upload.fileID;
};

// 云函数入口函数
exports.main = async (event, context) => {
  console.info(`==================== 云函数被调用事件：`, event)

  // 校验版本
  const { version } = event;
  if (!version) {
    throw {code: 400, message: "参数异常"};
  }

  switch (event.type) {
    case "getOpenId":
      return await getOpenId();
    case "getMiniProgramCode":
      return await getMiniProgramCode();

    // 节假日
    case "getHolidayData":
      // 获取节假日数据
      return await getHolidayData(event.data);
    case "getWeekendData":
      // 获取周末数据
      return await getWeekendData(event.data);

    // 公告系统
    case "getAnnouncementList":
      // 获取公告列表
      return await getAnnouncementList(event.data);

    // 用户系统
    case "getUserInfo":
      // 获取用户信息
      return await getUserInfo(event);
    case "updateUserInfo":
      // 更新用户信息
      return await updateUserInfo(event);
    case "getVipRecords":
      // 获取VIP记录
      return await getVipRecords(event.data);
    case "getVipRecordsStats":
      // 获取VIP记录统计
      return await getVipRecordsStats(event.data);
    case "getUserRedemptionCodes":
      // 获取用户兑换码
      return await getUserRedemptionCodes(event.data);
    case "useRedemptionCode":
      // 使用兑换码
      return await useRedemptionCode(event.data);

    // 签到相关API
    case "checkIn":
      // 执行签到
      return await checkIn(event.data);
    case "getCheckInStatus":
      // 获取签到状态
      return await getCheckInStatus(event.data);
    case "getCheckInCalendar":
      // 获取签到日历
      return await getCheckInCalendar(event.data);
    case "getCheckInHistory":
      // 获取签到历史
      return await getCheckInHistory(event.data);

    // 积分相关API
    case "getPointsBalance":
      // 获取积分余额
      return await getPointsBalance(event.data);
    case "getPointsRecords":
      // 获取积分记录
      return await getPointsRecords(event.data);
    case "getPointsStats":
      // 获取积分统计
      return await getPointsStats(event.data);

    // 邀请好友相关API
    case "processInvitation":
      // 处理邀请绑定
      return await processInvitation(event.data);
    case "getInvitationStats":
      // 获取邀请统计
      return await getInvitationStats(event.data);

    // 商店相关API
    case "getStoreItems":
      // 获取商店商品
      return await getStoreItems(event.data);

    case "purchaseItem":
      // 购买商品
      return await purchaseItem(event.data);
    case "getMyRedemptionCodes":
      // 获取我的兑换码
      return await getMyRedemptionCodes(event.data);
    case "redeemCode":
      // 使用兑换码
      return await redeemCode(event.data);

    // 系统内部API
    case "initializeStoreItems":
      // 初始化商店商品（系统内部调用）
      return await initializeStoreItems(event.data);
    case "testDatabase":
      // 测试数据库连接
      return await testDatabase(event.data);

    // 数据同步
    case "getCloudDataInfo":
      // 获取云端数据信息
      return await getCloudDataInfo(event.data);
    case "downloadUserData":
      // 下载用户数据
      return await downloadUserData(event.data);
    case "uploadUserData":
      // 上传用户数据
      return await uploadUserData(event.data);
    case "getHistoryDataList":
      // 获取历史数据列表
      return await getHistoryDataList(event.data);
    case "getHistoryData":
      // 获取指定历史数据
      return await getHistoryData(event.data);
    case "getCloudDataStats":
      // 获取云端数据统计信息
      return await getCloudDataStats(event.data);
    case "clearCloudData":
      // 清空云端数据
      return await clearCloudData(event.data);

    // 摸鱼状态
    case "startFishingStatus":
      // 开始摸鱼状态记录
      return await startFishingStatus(event.data);
    case "endFishingStatus":
      // 结束摸鱼状态记录
      return await endFishingStatus(event.data);
    case "getCurrentFishingCount":
      // 获取当前摸鱼人数
      return await getCurrentFishingCount(event.data);
    case "cleanupExpiredFishingStatus":
      // 清理过期摸鱼状态
      return await cleanupExpiredFishingStatus(event.data);
    case "getUserFishingStatus":
      // 获取用户摸鱼状态
      return await getUserFishingStatus(event.data);

    // 反馈意见
    case "submitFeedback":
      // 提交反馈
      return await submitFeedback(event.data);
    case "getUserFeedbacks":
      // 获取用户反馈列表
      return await getUserFeedbacks(event.data);
    case "deleteFeedback":
      // 删除反馈
      return await deleteFeedback(event.data);
    case "getFeedbackStats":
      // 获取反馈统计
      return await getFeedbackStats(event.data);

    // 友情应用相关
    case "getFriendApps":
      // 获取友情应用列表
      return await getFriendApps(event.data);
    case "createFriendApp":
      // 创建友情应用
      return await createFriendApp(event.data);
    case "updateFriendApp":
      // 更新友情应用
      return await updateFriendApp(event.data);
    case "deleteFriendApp":
      // 删除友情应用
      return await deleteFriendApp(event.data);
    case "toggleFriendAppVisibility":
      // 切换友情应用可见性
      return await toggleFriendAppVisibility(event.data);
    case "getFriendAppStats":
      // 获取友情应用统计
      return await getFriendAppStats(event.data);

    // 配置相关API
    case "getAllConfigs":
      // 获取所有配置
      return await getAllConfigs(event.data);

    default:
      return {
        success: false,
        message: "未知的操作类型"
      };
  }
};
