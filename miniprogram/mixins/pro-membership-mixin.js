/**
 * Pro会员权益控制混入
 * 为页面提供响应式的权益控制功能
 */

const { 
  isProMember, 
  getProMembershipConfig, 
  checkFeaturePermission, 
  checkUsageLimit,
  getLimitInfo,
  createMembershipListener 
} = require('../utils/pro-membership.js')

/**
 * Pro会员权益控制混入
 * 使用方法：在页面的Page()调用前，使用Object.assign(pageOptions, ProMembershipMixin)
 */
const ProMembershipMixin = {
  data: {
    // 权益配置
    membershipConfig: {
      isPro: false,
      memberType: '普通用户',
      features: {},
      limits: {}
    }
  },

  /**
   * 页面加载时初始化权益控制
   */
  onLoad() {
    // 如果页面已有onLoad，需要手动调用this.initMembershipControl()
    this.initMembershipControl()
  },

  /**
   * 页面显示时更新权益状态
   */
  onShow() {
    // 如果页面已有onShow，需要手动调用this.updateMembershipConfig()
    this.updateMembershipConfig()
  },

  /**
   * 页面卸载时清理监听器
   */
  onUnload() {
    // 如果页面已有onUnload，需要手动调用this.cleanupMembershipListener()
    this.cleanupMembershipListener()
  },

  /**
   * 初始化权益控制
   */
  initMembershipControl() {
    // 初始化权益配置
    this.updateMembershipConfig()
    
    // 创建权益变化监听器
    this.membershipListener = createMembershipListener((newConfig, userInfo) => {
      console.log('权益配置变化:', newConfig)
      this.setData({
        membershipConfig: newConfig
      })
      
      // 调用页面自定义的权益变化处理函数
      if (typeof this.onMembershipConfigChange === 'function') {
        this.onMembershipConfigChange(newConfig, userInfo)
      }
    })
  },

  /**
   * 更新权益配置
   */
  updateMembershipConfig() {
    const config = getProMembershipConfig()
    this.setData({
      membershipConfig: config
    })
  },

  /**
   * 清理权益监听器
   */
  cleanupMembershipListener() {
    if (this.membershipListener) {
      this.membershipListener()
      this.membershipListener = null
    }
  },

  /**
   * 检查功能权限
   * @param {string} featureName - 功能名称
   * @returns {boolean} 是否有权限
   */
  checkFeaturePermission(featureName) {
    return checkFeaturePermission(featureName)
  },

  /**
   * 检查使用限制
   * @param {string} limitType - 限制类型
   * @param {number} currentValue - 当前值
   * @returns {boolean} 是否在限制内
   */
  checkUsageLimit(limitType, currentValue) {
    return checkUsageLimit(limitType, currentValue)
  },

  /**
   * 获取限制信息
   * @param {string} limitType - 限制类型
   * @returns {Object} 限制信息
   */
  getLimitInfo(limitType) {
    return getLimitInfo(limitType)
  },

  /**
   * 显示权益限制提示
   * @param {string} featureName - 功能名称
   * @param {string} description - 功能描述
   */
  showMembershipLimitTip(featureName, description = '此功能') {
    wx.showToast({
      title: `${description}需要Pro会员`,
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 显示使用限制提示
   * @param {string} limitType - 限制类型
   * @param {string} description - 限制描述
   */
  showUsageLimitTip(limitType, description = '此功能') {
    const limitInfo = this.getLimitInfo(limitType)
    wx.showToast({
      title: `${description}已达到限制 (${limitInfo.displayText})`,
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 跳转到会员中心
   */
  goToMembershipCenter() {
    wx.navigateTo({
      url: '/pages/membership/index'
    })
  }
}

module.exports = ProMembershipMixin
