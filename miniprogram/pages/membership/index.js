// 会员页面
import { api } from '../../core/api/index.js'
const { getUserManager } = require('../../core/managers/user-manager.js')
import { formatRefreshTime } from '../../utils/time-utils.js'
const { isProUser, getUserTypeText } = require('../../utils/pro-benefits.js')

Page({
  data: {
    // 用户信息
    userInfo: {
      no: null,
      avatar: '',
      nickname: '加载中...',
      vip: {
        status: false,
        expiredAt: null
      },
      points: 0,
      isLoggedIn: false
    },

    // VIP到期时间显示文本
    vipExpireText: '',

    // 会员统计数据
    membershipStats: {
      membershipDays: 0,
      membershipUsageDays: 0,
      apiCallsToday: 0,
      apiCallsThisMonth: 0,
      membershipLevel: {
        level: 0,
        name: '普通用户',
        icon: '👤',
        color: '#6c757d'
      },
      joinDate: '',
      totalPoints: 0
    },



    // VIP记录相关
    vipRecords: [],
    vipRecordsStats: {
      totalDays: 0,
      totalRecords: 0
    },
    showVipRecordsModal: false,

    // 兑换码模态框
    showRedeemModal: false,
    defaultRedeemCode: '',

    // 邀请好友模态框
    showInviteModal: false,
    inviteCode: '',



    // 会员信息
    membershipInfo: {
      expireDate: '',
      expireDateText: '',
      daysRemaining: 0,
      isNewUser: false,
      canWatchAd: true,
      lastAdWatchTime: null
    }
  },

  /**
   * 页面加载时
   */
  onLoad() {
    console.log('会员页面加载')
    this.initializePage()
  },

  /**
   * 初始化页面
   */
  async initializePage() {
    try {
      // 直接获取用户管理器
      this.userManager = getUserManager()

      // 确保用户管理器已初始化
      if (!this.userManager.isUserLoggedIn()) {
        await this.userManager.initialize()
      }

      // 加载页面数据
      await Promise.all([
        this.loadUserInfo(),
        this.loadMembershipStats(),
        this.loadVipRecordsStats()
      ])
    } catch (error) {
      console.error('页面初始化失败:', error)
      wx.showToast({
        title: '页面加载失败，请重试',
        icon: 'none'
      })
    }
  },



  /**
   * 页面显示时
   */
  onShow() {
    console.log('会员页面显示')

    // 确保用户管理器存在
    if (!this.userManager) {
      this.userManager = getUserManager()
    }

    // 刷新数据
    this.loadUserInfo()
    this.loadMembershipStats()
    this.loadVipRecordsStats()
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    try {
      // 先从本地获取基础信息
      const localUserInfo = this.userManager.getUserInfo()
      const isLoggedIn = this.userManager.isUserLoggedIn()

      if (!isLoggedIn) {
        this.setData({
          'userInfo.nickname': '未登录',
          'userInfo.vip': { status: false, expiredAt: null },
          'userInfo.points': 0,
          'userInfo.isLoggedIn': false
        })
        return
      }

      console.log('[Membership] 开始获取用户信息')

      // 从API获取最新用户信息
      const result = await api.user.getUserInfo()

      if (result.success) {
        const userInfo = result.data

        this.setData({
          'userInfo.no': userInfo.no || null,
          'userInfo.avatar': userInfo.avatar || '',
          'userInfo.nickname': userInfo.nickname || '未设置昵称',
          'userInfo.vip': userInfo.vip || { status: false, expiredAt: null },
          'userInfo.points': userInfo.points || 0,
          'userInfo.isLoggedIn': true
        })

        // 更新本地缓存
        this.userManager.updateUserInfo(userInfo)

        console.log('[Membership] 用户信息加载完成:', userInfo)
        console.log('[Membership] VIP信息:', userInfo.vip)
        console.log('[Membership] 设置后的用户信息:', this.data.userInfo)

        // 计算VIP到期时间显示文本
        this.updateVipExpireText()

        // 更新广告观看状态
        this.setData({
          canWatchAd: this.checkCanWatchAd()
        })
      } else {
        console.error('[Membership] 获取用户信息失败:', result.message)
        // 使用本地缓存信息
        this.setData({
          'userInfo.no': localUserInfo ? localUserInfo.no : null,
          'userInfo.avatar': localUserInfo ? localUserInfo.avatar : '',
          'userInfo.nickname': localUserInfo ? localUserInfo.nickname : '未登录',
          'userInfo.vip': localUserInfo ? localUserInfo.vip : { status: false, expiredAt: null },
          'userInfo.points': localUserInfo ? (localUserInfo.points || 0) : 0,
          'userInfo.isLoggedIn': isLoggedIn
        })
        this.updateVipExpireText()
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
      // 使用本地缓存信息作为备用
      const localUserInfo = this.userManager.getUserInfo()
      const isLoggedIn = this.userManager.isUserLoggedIn()

      this.setData({
        'userInfo.no': localUserInfo ? localUserInfo.no : null,
        'userInfo.avatar': localUserInfo ? localUserInfo.avatar : '',
        'userInfo.nickname': localUserInfo ? localUserInfo.nickname : '未登录',
        'userInfo.vip': localUserInfo ? localUserInfo.vip : { status: false, expiredAt: null },
        'userInfo.points': localUserInfo ? (localUserInfo.points || 0) : 0,
        'userInfo.isLoggedIn': isLoggedIn
      })
      this.updateVipExpireText()
    }
  },

  /**
   * 更新VIP到期时间显示文本
   */
  updateVipExpireText() {
    const userInfo = this.data.userInfo
    console.log('更新VIP到期时间，用户信息:', userInfo)

    if (userInfo && userInfo.vip && userInfo.vip.status && userInfo.vip.expiredAt) {
      const expireDate = new Date(userInfo.vip.expiredAt)
      const now = new Date()
      const daysRemaining = Math.ceil((expireDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

      console.log('VIP到期日期:', expireDate, '剩余天数:', daysRemaining)

      if (daysRemaining > 0) {
        this.setData({
          vipExpireText: `还有${daysRemaining}天到期`
        })
      } else {
        this.setData({
          vipExpireText: '已过期'
        })
      }
    } else {
      console.log('用户非VIP或缺少VIP信息')
      this.setData({
        vipExpireText: ''
      })
    }
  },

  /**
   * 加载会员统计数据
   */
  async loadMembershipStats() {
    try {
      console.log('[Membership] 开始加载会员统计')

      const result = await api.user.getMembershipStats()

      if (result.success) {
        console.log('[Membership] 会员统计数据加载成功:', result.data)
        this.setData({
          membershipStats: result.data
        })
      } else {
        console.error('[Membership] 获取会员统计失败:', result.message)
      }
    } catch (error) {
      console.error('[Membership] 加载会员统计失败:', error)
    }
  },



  /**
   * 设置默认会员信息
   */
  setDefaultMembershipInfo(isVip) {
    const isNewUser = this.checkIsNewUser()

    this.setData({
      membershipInfo: {
        expireDate: isVip ? '获取中...' : '',
        expireDateText: isVip ? '获取中...' : '',
        daysRemaining: 0,
        isNewUser: isNewUser,
        canWatchAd: this.checkCanWatchAd(),
        lastAdWatchTime: null
      }
    })
  },

  /**
   * 检查是否为新用户
   */
  checkIsNewUser() {
    try {
      const userInfo = this.data.userInfo
      return !userInfo.userId || userInfo.userId === '未知'
    } catch (error) {
      console.error('检查新用户状态失败:', error)
      return false
    }
  },

  /**
   * 检查是否可以观看广告
   */
  checkCanWatchAd() {
    // 简化逻辑：每天可以观看一次广告
    const lastAdWatchTime = wx.getStorageSync('lastAdWatchTime')
    if (!lastAdWatchTime) {
      return true
    }

    const lastWatchDate = new Date(lastAdWatchTime).toDateString()
    const today = new Date().toDateString()
    
    return lastWatchDate !== today
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  /**
   * 加载VIP记录统计
   */
  async loadVipRecordsStats() {
    try {
      // 获取VIP记录统计
      const result = await api.user.getVipRecordsStats({
        showLoading: false,
        showError: false
      })

      if (result.success) {
        this.setData({
          vipRecordsStats: result.data
        })
      }
    } catch (error) {
      console.error('加载VIP记录统计失败:', error)
    }
  },

  /**
   * 查看VIP记录
   */
  async onViewVipRecords() {
    try {
      wx.showLoading({ title: '加载中...' })

      console.log('[Membership] 开始加载VIP记录')

      const result = await api.user.getVipRecords({ limit: 50 })

      wx.hideLoading()

      if (result.success) {
        // 格式化记录数据
        const formattedRecords = result.data.map(record => ({
          ...record,
          dateText: this.formatDateTime(record.createdAt)
        }))

        this.setData({
          vipRecords: formattedRecords,
          showVipRecordsModal: true
        })
        console.log('[Membership] VIP记录加载成功')
      } else {
        wx.showToast({
          title: result.message || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('[Membership] 加载VIP记录失败:', error)
      wx.showToast({
        title: error.message || '网络异常，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(dateStr) {
    if (!dateStr) return ''
    
    const date = new Date(dateStr)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days === 0) {
      return '今天'
    } else if (days === 1) {
      return '昨天'
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  },

  /**
   * 关闭VIP记录模态框
   */
  onCloseVipRecordsModal() {
    this.setData({
      showVipRecordsModal: false
    })
  },



  /**
   * 跳转到积分商店
   */
  onGoToStore() {
    wx.navigateTo({
      url: '/pages/store/index'
    })
  },

  /**
   * 显示兑换码模态框
   */
  onShowRedeemModal() {
    this.setData({
      showRedeemModal: true,
      defaultRedeemCode: ''
    })
  },

  /**
   * 关闭兑换码模态框
   */
  onCloseRedeemModal() {
    this.setData({
      showRedeemModal: false
    })
  },

  /**
   * 兑换成功回调
   */
  onRedeemSuccess(e) {
    console.log('兑换成功:', e.detail)
    // 刷新用户信息和会员信息
    this.loadUserInfo()
    this.loadMembershipInfo()
    this.loadVipRecordsStats()
  },



  /**
   * 显示升级选项
   */
  onShowUpgradeOptions() {
    wx.showActionSheet({
      itemList: ['🎫 兑换码', '👥 邀请好友'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.onShowRedeemModal()
            break
          case 1:
            this.onShowInviteModal()
            break
        }
      }
    })
  },

  /**
   * 专属功能 - 云端同步
   */
  onGoToCloudSync() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 专属功能 - 高级分析
   */
  onGoToAdvancedAnalytics() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 专属功能 - 历史数据
   */
  onGoToHistoryData() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 专属功能 - 数据导出
   */
  onGoToDataExport() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },



  /**
   * 显示邀请模态框
   */
  onShowInviteModal() {
    this.setData({
      showInviteModal: true
    })
  },

  /**
   * 关闭邀请模态框
   */
  onCloseInviteModal() {
    this.setData({
      showInviteModal: false
    })
  },

  /**
   * 生成邀请码（使用用户ID作为邀请码）
   */
  async onGenerateInviteCode() {
    try {
      // 使用用户ID作为邀请码，这样更简单且实用
      const userInfo = this.data.userInfo
      if (!userInfo || !userInfo._id) {
        wx.showToast({
          title: '用户信息未加载，请稍后重试',
          icon: 'none'
        })
        return
      }

      // 直接使用用户ID作为邀请码
      const inviteCode = userInfo._id

      this.setData({
        inviteCode: inviteCode
      })

      wx.showToast({
        title: '邀请码生成成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('生成邀请码失败:', error)
      wx.showToast({
        title: '生成邀请码失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 复制邀请码
   */
  onCopyInviteCode() {
    wx.setClipboardData({
      data: this.data.inviteCode,
      success: () => {
        wx.showToast({
          title: '邀请码已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 分享邀请码
   */
  onShareInviteCode() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  /**
   * 输入激活码续期
   */
  onInputActivationCode() {
    console.log('输入激活码续期')

    wx.showModal({
      title: '激活码续期',
      content: '请联系客服获取激活码，输入激活码即可获得或续期VIP会员。\n\n功能开发中，敬请期待！',
      confirmText: '我知道了',
      showCancel: false
    })
  },

  /**
   * 观看广告获取会员
   */
  onWatchAdForMembership() {
    console.log('观看广告获取会员')

    if (!this.checkCanWatchAd()) {
      wx.showToast({
        title: '今日观看次数已用完',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '观看广告获取VIP',
      content: '观看完整的激励视频广告，即可获得VIP会员时长。\n\n每日限制观看次数。\n\n功能开发中，敬请期待！',
      confirmText: '开始观看',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // TODO: 实现激励视频广告逻辑
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  /**
   * 检查是否可以观看广告
   */
  checkCanWatchAd() {
    const now = new Date()
    const today = now.toDateString()

    // 检查今日是否已观看过广告（简化逻辑）
    // 这里可以根据实际需求实现更复杂的逻辑
    return true // 暂时返回true，实际应该检查用户的观看记录
  },



  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新会员页面')
    Promise.all([
      this.loadUserInfo(),
      this.loadMembershipStats(),
      this.loadVipRecordsStats()
    ]).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  }
})
