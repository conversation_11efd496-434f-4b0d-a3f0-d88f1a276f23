<!-- 会员页面 -->
<view class="membership-page">
  <!-- 会员状态卡片 -->
  <view class="membership-status-card {{userInfo.vip.status ? 'vip' : ''}}">
    <view class="status-content">
      <view class="status-left">
        <view class="status-icon-container">
          <text class="crown-icon">{{membershipStats.membershipLevel.icon}}</text>
          <view wx:if="{{userInfo.vip.status}}" class="level-badge" style="background: {{membershipStats.membershipLevel.color}}">
            <text class="level-text">{{membershipStats.membershipLevel.name}}</text>
          </view>
        </view>
        <view class="status-info">
          <text class="status-title">{{userInfo.vip.status ? 'VIP会员' : '免费用户'}}</text>
          <text wx:if="{{userInfo.vip.status}}" class="status-subtitle">{{vipExpireText}}</text>
          <text wx:else class="status-subtitle">升级VIP享受更多权益</text>
          <view wx:if="{{userInfo.vip.status}}" class="status-stats">
            <text class="stat-item">已使用{{membershipStats.membershipUsageDays}}天</text>
            <text class="stat-item">剩余{{membershipStats.membershipDays}}天</text>
          </view>
        </view>
      </view>

      <view wx:if="{{!userInfo.vip.status}}" class="upgrade-badge" bindtap="onShowUpgradeOptions">
        <text class="upgrade-text">立即升级</text>
      </view>
    </view>
  </view>

  <!-- 会员使用统计 -->
  <view wx:if="{{userInfo.vip.status}}" class="usage-stats-section">
    <view class="section-header">
      <text class="section-title">使用统计</text>
      <text class="section-subtitle">本月数据概览</text>
    </view>

    <view class="stats-grid">
      <view class="stat-card">
        <view class="stat-icon">📊</view>
        <view class="stat-info">
          <text class="stat-value">{{membershipStats.apiCallsThisMonth}}</text>
          <text class="stat-label">API调用</text>
        </view>
      </view>
      <view class="stat-card">
        <view class="stat-icon">💰</view>
        <view class="stat-info">
          <text class="stat-value">{{membershipStats.totalPoints}}</text>
          <text class="stat-label">总积分</text>
        </view>
      </view>
      <view class="stat-card">
        <view class="stat-icon">📅</view>
        <view class="stat-info">
          <text class="stat-value">{{membershipStats.membershipUsageDays}}</text>
          <text class="stat-label">使用天数</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 会员权益展示 -->
  <view class="benefits-section">
    <view class="section-header">
      <text class="section-title">会员权益</text>
      <text class="section-subtitle">解锁更多强大功能</text>
    </view>

    <view class="benefits-comparison">
      <view class="comparison-header">
        <view class="comparison-item">
          <text class="comparison-title">功能</text>
        </view>
        <view class="comparison-item">
          <text class="comparison-title">免费用户</text>
        </view>
        <view class="comparison-item">
          <text class="comparison-title">VIP会员</text>
        </view>
      </view>

      <view class="comparison-row">
        <view class="comparison-item">
          <view class="feature-info">
            <text class="feature-icon">☁️</text>
            <text class="feature-name">数据同步</text>
          </view>
        </view>
        <view class="comparison-item">
          <text class="feature-unlimited">✅ 自动同步</text>
        </view>
        <view class="comparison-item">
          <text class="feature-unlimited">✅ 自动同步</text>
        </view>
      </view>

      <view class="comparison-row">
        <view class="comparison-item">
          <view class="feature-info">
            <text class="feature-icon">📊</text>
            <text class="feature-name">数据统计</text>
          </view>
        </view>
        <view class="comparison-item">
          <text class="feature-limit">❌ 不可用</text>
        </view>
        <view class="comparison-item">
          <text class="feature-unlimited">✅ 完整统计</text>
        </view>
      </view>

      <view class="comparison-row">
        <view class="comparison-item">
          <view class="feature-info">
            <text class="feature-icon">🗂️</text>
            <text class="feature-name">历史数据</text>
          </view>
        </view>
        <view class="comparison-item">
          <text class="feature-limit">❌ 不可用</text>
        </view>
        <view class="comparison-item">
          <text class="feature-unlimited">✅ 7天历史</text>
        </view>
      </view>

      <view class="comparison-row">
        <view class="comparison-item">
          <view class="feature-info">
            <text class="feature-icon">🎨</text>
            <text class="feature-name">主题切换</text>
          </view>
        </view>
        <view class="comparison-item">
          <text class="feature-limit">默认主题</text>
        </view>
        <view class="comparison-item">
          <text class="feature-unlimited">✅ 所有主题</text>
        </view>
      </view>

      <view class="comparison-row">
        <view class="comparison-item">
          <view class="feature-info">
            <text class="feature-icon">💼</text>
            <text class="feature-name">工作履历</text>
          </view>
        </view>
        <view class="comparison-item">
          <text class="feature-limit">最多 2 个</text>
        </view>
        <view class="comparison-item">
          <text class="feature-unlimited">✅ 无限制</text>
        </view>
      </view>

      <view class="comparison-row">
        <view class="comparison-item">
          <view class="feature-info">
            <text class="feature-icon">✨</text>
            <text class="feature-name">批量复制</text>
          </view>
        </view>
        <view class="comparison-item">
          <text class="feature-limit">❌ 不可用</text>
        </view>
        <view class="comparison-item">
          <text class="feature-unlimited">✅ 可用</text>
        </view>
      </view>
    </view>

    <!-- 权益说明 -->
    <view class="benefits-note">
      <text class="note-text">* 数据同步功能目前所有用户都可使用，后续可能会移除普通用户的自动同步权益</text>
    </view>
  </view>

  <!-- VIP续期功能 -->
  <view wx:if="{{userInfo.vip.status}}" class="extend-membership-section">
    <view class="section-header">
      <text class="section-title">续期会员</text>
      <text class="section-subtitle">延长您的VIP会员时长</text>
    </view>

    <view class="extend-options">
      <view class="extend-option" bindtap="onInputActivationCode">
        <view class="option-icon">🔑</view>
        <view class="option-info">
          <text class="option-title">激活码续期</text>
          <text class="option-desc">输入激活码延长会员时长</text>
        </view>
        <view class="option-arrow">›</view>
      </view>

      <view wx:if="{{canWatchAd}}" class="extend-option" bindtap="onWatchAdForMembership">
        <view class="option-icon">📺</view>
        <view class="option-info">
          <text class="option-title">观看广告续期</text>
          <text class="option-desc">观看广告获得额外会员时长</text>
        </view>
        <view class="option-arrow">›</view>
      </view>

      <view class="extend-option" bindtap="onShowRedeemModal">
        <view class="option-icon">🎫</view>
        <view class="option-info">
          <text class="option-title">兑换码续期</text>
          <text class="option-desc">使用兑换码延长会员时长</text>
        </view>
        <view class="option-arrow">›</view>
      </view>
    </view>
  </view>

  <!-- 会员专属功能 -->
  <view wx:if="{{userInfo.vip.status}}" class="exclusive-features-section">
    <view class="section-header">
      <text class="section-title">专属功能</text>
      <text class="section-subtitle">VIP会员专享快捷入口</text>
    </view>

    <view class="features-grid">
      <view class="feature-card" bindtap="onGoToCloudSync">
        <view class="feature-icon">☁️</view>
        <text class="feature-name">云端同步</text>
        <text class="feature-desc">管理同步设置</text>
      </view>
      <view class="feature-card" bindtap="onGoToAdvancedAnalytics">
        <view class="feature-icon">📊</view>
        <text class="feature-name">高级分析</text>
        <text class="feature-desc">查看详细报表</text>
      </view>
      <view class="feature-card" bindtap="onGoToHistoryData">
        <view class="feature-icon">🗂️</view>
        <text class="feature-name">历史数据</text>
        <text class="feature-desc">访问全部记录</text>
      </view>
      <view class="feature-card" bindtap="onGoToDataExport">
        <view class="feature-icon">📤</view>
        <text class="feature-name">数据导出</text>
        <text class="feature-desc">导出全格式</text>
      </view>
    </view>
  </view>



  <!-- 获取VIP方式 -->
  <view wx:if="{{!userInfo.vip.status}}" class="get-vip-section">
    <view class="section-header">
      <text class="section-title">获取VIP会员</text>
      <text class="section-subtitle">多种方式轻松升级</text>
    </view>

    <view class="get-vip-options">
      <view class="vip-option" bindtap="onClaimNewUserBenefit">
        <view class="option-icon">🎁</view>
        <view class="option-info">
          <text class="option-title">新用户福利</text>
          <text class="option-desc">免费获得14天VIP会员</text>
        </view>
        <view class="option-arrow">›</view>
      </view>

      <view class="vip-option" bindtap="onShowRedeemModal">
        <view class="option-icon">🎫</view>
        <view class="option-info">
          <text class="option-title">兑换码</text>
          <text class="option-desc">输入兑换码获取VIP会员</text>
        </view>
        <view class="option-arrow">›</view>
      </view>

      <view class="vip-option" bindtap="onShowInviteModal">
        <view class="option-icon">👥</view>
        <view class="option-info">
          <text class="option-title">邀请好友</text>
          <text class="option-desc">邀请好友注册获得VIP奖励</text>
        </view>
        <view class="option-arrow">›</view>
      </view>
    </view>
  </view>

  <!-- VIP记录 -->
  <view class="vip-records-section">
    <view class="section-header">
      <text class="section-title">VIP记录</text>
      <text class="section-subtitle">查看您的会员获取历史</text>
    </view>

    <view class="records-card" bindtap="onViewVipRecords">
      <view class="records-summary">
        <view class="summary-item">
          <text class="summary-label">总获取天数</text>
          <text class="summary-value">{{vipRecordsStats.totalDays || 0}}天</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">获取次数</text>
          <text class="summary-value">{{vipRecordsStats.totalRecords || 0}}次</text>
        </view>
      </view>
      <view class="records-arrow">›</view>
    </view>
  </view>

  <!-- 推荐奖励 -->
  <view class="invite-section">
    <view class="section-header">
      <text class="section-title">推荐奖励</text>
      <text class="section-subtitle">邀请好友，共享VIP权益</text>
    </view>

    <view class="invite-card" bindtap="onShowInviteModal">
      <view class="invite-content">
        <view class="invite-left">
          <view class="invite-icon">👥</view>
          <view class="invite-info">
            <text class="invite-title">邀请好友获得VIP</text>
            <text class="invite-desc">每邀请1位好友注册，您和好友都可获得7天VIP</text>
          </view>
        </view>
        <view class="invite-right">
          <view class="invite-btn">
            <text class="btn-text">立即邀请</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 会员反馈 -->
  <view class="feedback-section">
    <view class="section-header">
      <text class="section-title">会员反馈</text>
      <text class="section-subtitle">您的建议让我们更好</text>
    </view>

    <view class="feedback-card" bindtap="onShowFeedbackModal">
      <view class="feedback-content">
        <view class="feedback-left">
          <view class="feedback-icon">💬</view>
          <view class="feedback-info">
            <text class="feedback-title">意见建议</text>
            <text class="feedback-desc">告诉我们您的想法和建议</text>
          </view>
        </view>
        <view class="feedback-right">
          <text class="feedback-arrow">›</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 兑换码模态框 -->
  <redeem-code-modal 
    show="{{showRedeemModal}}" 
    defaultCode="{{defaultRedeemCode}}"
    bind:close="onCloseRedeemModal"
    bind:success="onRedeemSuccess">
  </redeem-code-modal>

  <!-- VIP记录模态框 -->
  <view wx:if="{{showVipRecordsModal}}" class="modal-overlay" bindtap="onCloseVipRecordsModal">
    <view class="modal-container" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">VIP获取记录</text>
        <view class="modal-close" bindtap="onCloseVipRecordsModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 统计信息 -->
        <view wx:if="{{vipRecordsStats}}" class="records-stats">
          <view class="stats-item">
            <text class="stats-label">总获取天数</text>
            <text class="stats-value">{{vipRecordsStats.totalDays}}天</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">获取次数</text>
            <text class="stats-value">{{vipRecordsStats.totalRecords}}次</text>
          </view>
        </view>

        <!-- 记录列表 -->
        <view class="records-list">
          <view wx:if="{{vipRecords.length === 0}}" class="empty-records">
            <text class="empty-text">暂无VIP获取记录</text>
          </view>

          <block wx:else>
            <view wx:for="{{vipRecords}}" wx:key="_id" class="record-item">
              <view class="record-header">
                <text class="record-days">+{{item.days}}天</text>
                <text class="record-date">{{item.dateText}}</text>
              </view>
              <view class="record-content">
                <text class="record-source">{{item.source}}</text>
                <text wx:if="{{item.description}}" class="record-desc">{{item.description}}</text>
              </view>
            </view>
          </block>
        </view>
      </view>

      <view class="modal-footer">
        <view class="modal-btn secondary" bindtap="onCloseVipRecordsModal">关闭</view>
      </view>
    </view>
  </view>

  <!-- 邀请好友模态框 -->
  <view wx:if="{{showInviteModal}}" class="modal-overlay" bindtap="onCloseInviteModal">
    <view class="modal-container" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">邀请好友</text>
        <view class="modal-close" bindtap="onCloseInviteModal">×</view>
      </view>

      <view class="modal-body">
        <view class="invite-modal-content">
          <view class="invite-reward-info">
            <text class="reward-title">邀请奖励</text>
            <text class="reward-desc">您和好友都可获得7天VIP会员</text>
          </view>

          <view wx:if="{{inviteCode}}" class="invite-code-section">
            <text class="code-label">您的邀请码</text>
            <view class="code-container">
              <text class="invite-code-text">{{inviteCode}}</text>
              <view class="copy-btn" bindtap="onCopyInviteCode">复制</view>
            </view>
          </view>

          <view class="invite-actions">
            <view class="action-btn primary" bindtap="onGenerateInviteCode">
              <text class="btn-text">{{inviteCode ? '重新生成' : '生成邀请码'}}</text>
            </view>
            <view wx:if="{{inviteCode}}" class="action-btn secondary" bindtap="onShareInviteCode">
              <text class="btn-text">分享给好友</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 反馈模态框 -->
  <view wx:if="{{showFeedbackModal}}" class="modal-overlay" bindtap="onCloseFeedbackModal">
    <view class="modal-container" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">会员反馈</text>
        <view class="modal-close" bindtap="onCloseFeedbackModal">×</view>
      </view>

      <view class="modal-body">
        <view class="feedback-form">
          <view class="form-group">
            <text class="form-label">反馈类型</text>
            <view class="feedback-types">
              <view
                class="type-option {{feedbackType === 'suggestion' ? 'active' : ''}}"
                bindtap="onSelectFeedbackType"
                data-type="suggestion">
                建议
              </view>
              <view
                class="type-option {{feedbackType === 'bug' ? 'active' : ''}}"
                bindtap="onSelectFeedbackType"
                data-type="bug">
                问题
              </view>
              <view
                class="type-option {{feedbackType === 'feature' ? 'active' : ''}}"
                bindtap="onSelectFeedbackType"
                data-type="feature">
                功能请求
              </view>
            </view>
          </view>

          <view class="form-group">
            <text class="form-label">反馈内容</text>
            <textarea
              class="feedback-textarea"
              placeholder="请详细描述您的建议或遇到的问题..."
              value="{{feedbackContent}}"
              bindinput="onFeedbackContentChange"
              maxlength="500">
            </textarea>
            <text class="char-count">{{feedbackContent.length}}/500</text>
          </view>

          <view class="form-group">
            <text class="form-label">联系方式（可选）</text>
            <input
              class="feedback-input"
              placeholder="微信号或邮箱，便于我们联系您"
              value="{{feedbackContact}}"
              bindinput="onFeedbackContactChange">
            </input>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <view class="modal-btn secondary" bindtap="onCloseFeedbackModal">取消</view>
        <view class="modal-btn primary" bindtap="onSubmitFeedback">提交反馈</view>
      </view>
    </view>
  </view>
</view>
